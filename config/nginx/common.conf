# This file contains shared settings that can be included in server blocks

# Common settings
userid_name 'uid';
userid_path '/; HttpOnly';
userid_expires 365d;
userid         on;
server_tokens off;

# Gzip configuration
gzip on;
gzip_comp_level 3;
gzip_static on;
gzip_min_length 2048;
gzip_buffers      16 8k;
gzip_vary         on;
gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/javascript application/xml
image/svg+xml application/xml+rss image/x-icon image/bmp;
gzip_disable "msie6";

root /usr/share/nginx/html/;

# Proxy settings
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;

large_client_header_buffers 4 32k;
client_max_body_size 32m;

proxy_connect_timeout 300;
proxy_send_timeout 300;
proxy_read_timeout 300;
send_timeout 300;
expires -1;
etag off;


