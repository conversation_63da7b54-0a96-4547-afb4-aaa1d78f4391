# Upstream configuration
upstream upstream_mapi {
  server ${MAPI} fail_timeout=30s max_fails=100;
}

upstream upstream_softgate_mapi {
  server ${SOFTGATE_MAPI} fail_timeout=30s max_fails=100;
}

# Default server block (fallback)
server {
  listen 80 default_server;
  server_name _;

  include /usr/share/nginx/common.conf;
  include /usr/share/nginx/locations.conf;

  location /api/config {
    default_type application/json;
    return 200 '{
      "host": "$host",
      "bridge": "${BRIDGE_URL}",
      "loginUrl": "${LOGIN_URL}",
      "hubs": {
        "casino": "${CASINO_HUB_URL}",
        "engagement": "${ENGAGEMENT_HUB_URL}",
        "analytics": "${DATA_HUB_URL}",
        "studio": "${STUDIO_HUB_URL}"
      }
    }';
  }

  location /mapi/ {
    proxy_pass http://upstream_mapi/;
  }
}

# Softgate virtual server
server {
  listen 80;
  server_name ${SOFTGATE_HOST};

  include /usr/share/nginx/common.conf;
  include /usr/share/nginx/locations.conf;

  location /api/config {
    default_type application/json;
    return 200 '{
      "host": "$host",
      "bridge": "${SOFTGATE_BRIDGE_URL}",
      "loginUrl": "${SOFTGATE_LOGIN_URL}",
      "hubs": {
        "casino": "${SOFTGATE_CASINO_HUB_URL}",
        "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",
        "analytics": "${SOFTGATE_DATA_HUB_URL}",
        "studio": "${SOFTGATE_STUDIO_HUB_URL}"
      }
    }';
  }

  location /mapi/ {
    proxy_pass http://upstream_softgate_mapi/;
  }
}
