# Nginx Virtual Server Migration

## Overview
This document describes the migration from a single nginx server block with conditional logic to separate virtual server blocks for better maintainability and performance.

## Changes Made

### 1. Updated `config/nginx/template.conf`
**Before:** Single server block with complex conditional logic using `if` statements to handle different hosts.

**After:** Separate virtual server blocks:
- **Default server block**: Handles all requests that don't match specific server names (fallback)
- **Softgate virtual server**: Dedicated server block for Softgate host requests

### 2. Created `config/nginx/locations.conf`
- Extracted common location blocks (especially the root `/` location with CORS headers)
- Shared across all virtual servers to avoid duplication
- Contains the main application routing and CORS configuration

### 3. Updated `config/nginx/common.conf`
- Removed duplicate location block that's now in `locations.conf`
- Kept all other common settings (gzip, proxy settings, etc.)

### 4. Updated `config/nginx/run.sh`
- Added copying of the new `locations.conf` file
- Maintained environment variable substitution for the main template

## Architecture Benefits

### Before (Single Server Block)
```nginx
server {
  listen 80 default_server;
  server_name localhost;
  
  location /api/config {
    if ($host = $SOFTGATE_HOST) {
      # Softgate config
    }
    if ($host != $SOFTGATE_HOST) {
      # Default config
    }
  }
}
```

### After (Virtual Servers)
```nginx
# Default server
server {
  listen 80 default_server;
  server_name _;
  # Default configuration
}

# Softgate server
server {
  listen 80;
  server_name ${SOFTGATE_HOST};
  # Softgate-specific configuration
}
```

## Key Improvements

1. **Performance**: Eliminates conditional `if` statements that are processed on every request
2. **Maintainability**: Clear separation of concerns between different host configurations
3. **Scalability**: Easy to add new virtual servers for additional hosts
4. **Debugging**: Easier to troubleshoot issues specific to each virtual server
5. **Configuration clarity**: Each server block is self-contained and easier to understand

## Configuration Structure

```
config/nginx/
├── template.conf      # Main nginx configuration with virtual servers
├── common.conf        # Shared settings (gzip, proxy, etc.)
├── locations.conf     # Shared location blocks
└── run.sh            # Startup script with environment substitution
```

## Environment Variables

The configuration uses the same environment variables as before:
- `SOFTGATE_HOST`: Hostname for the Softgate virtual server
- `MAPI` / `SOFTGATE_MAPI`: API endpoints for each server
- Various URL configurations for each environment

## Testing

To test the configuration:
1. Build the Docker image: `docker build -t sw-ubo-hub-bridge .`
2. Run with different host headers to test virtual server routing
3. Verify that each virtual server serves the correct configuration via `/api/config`

## Backward Compatibility

The migration maintains full backward compatibility:
- Same environment variables
- Same API endpoints
- Same functionality
- Same Docker build process
